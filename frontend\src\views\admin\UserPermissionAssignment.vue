<template>
  <div class="user-permission-assignment">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">用户权限分配</h1>
      <p class="page-subtitle">为用户分配系统配置项的查看和编辑权限</p>
    </div>

    <!-- 系统权限概览 -->
    <div class="permission-overview">
      <h2>系统权限分布</h2>
      <div v-if="systems.length === 0 && !loading" class="no-systems">
        <el-empty description="暂无系统权限数据，请先为用户分配权限" />
      </div>
      <div v-else class="systems-grid">
        <div
          v-for="system in systems"
          :key="system.id"
          class="system-card"
        >
          <div class="system-header">
            <h3>{{ system.name }}</h3>
          </div>

          <div class="permission-stats">
            <div class="stat-item">
              <span class="stat-label">查看权限用户：</span>
              <div class="user-tags">
                <el-tag
                  v-for="user in getSystemReadUsers(system.id)"
                  :key="user._id"
                  size="small"
                  type="info"
                  @click="scrollToUser(user._id)"
                  class="clickable-tag"
                >
                  {{ user.username }}
                </el-tag>
                <span v-if="getSystemReadUsers(system.id).length === 0" class="no-users">无</span>
              </div>
            </div>

            <div class="stat-item">
              <span class="stat-label">编辑权限用户：</span>
              <div class="user-tags">
                <el-tag
                  v-for="user in getSystemWriteUsers(system.id)"
                  :key="user._id"
                  size="small"
                  type="warning"
                  @click="scrollToUser(user._id)"
                  class="clickable-tag"
                >
                  {{ user.username }}
                </el-tag>
                <span v-if="getSystemWriteUsers(system.id).length === 0" class="no-users">无</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 用户权限分配区域 -->
    <div class="users-permission-section">
      <h2>用户权限配置</h2>
      <div class="users-grid" v-loading="loading">
        <div
          v-for="user in users"
          :key="user._id"
          :id="`user-card-${user._id}`"
          class="user-card"
        >
          <div class="user-header">
            <div class="user-info">
              <h3>{{ user.username }}</h3>
              <el-tag :type="user.role === 'admin' ? 'danger' : 'primary'" size="small">
                {{ user.role === 'admin' ? '管理员' : '用户' }}
              </el-tag>
            </div>
            <div class="user-actions">
              <el-button
                type="success"
                size="small"
                @click="saveUserPermissions(user._id)"
                :loading="savingUsers.includes(user._id)"
                :disabled="!hasPermissionChanges(user._id)"
              >
                <el-icon><Check /></el-icon>
                确定
              </el-button>
            </div>
          </div>

          <div class="permission-types">
            <!-- 查看权限 -->
            <div class="permission-type-card">
              <div class="permission-type-header">
                <h4>查看权限 (Read)</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="openPermissionDialog(user._id, 'read')"
                >
                  配置
                </el-button>
              </div>
              <div class="permission-summary">
                <div v-if="getUserPermissionSummary(user._id, 'read').length === 0" class="no-permissions">
                  暂无查看权限
                </div>
                <div v-else class="permission-items">
                  <el-tag
                    v-for="item in getUserPermissionSummary(user._id, 'read')"
                    :key="`${item.systemId}-${item.configName}`"
                    size="small"
                    type="info"
                    class="permission-tag"
                  >
                    {{ item.systemName }} - {{ item.configName }}
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 编辑权限 -->
            <div class="permission-type-card">
              <div class="permission-type-header">
                <h4>编辑权限 (Write)</h4>
                <el-button
                  type="warning"
                  size="small"
                  @click="openPermissionDialog(user._id, 'write')"
                >
                  配置
                </el-button>
              </div>
              <div class="permission-summary">
                <div v-if="getUserPermissionSummary(user._id, 'write').length === 0" class="no-permissions">
                  暂无编辑权限
                </div>
                <div v-else class="permission-items">
                  <el-tag
                    v-for="item in getUserPermissionSummary(user._id, 'write')"
                    :key="`${item.systemId}-${item.configName}`"
                    size="small"
                    type="warning"
                    class="permission-tag"
                  >
                    {{ item.systemName }} - {{ item.configName }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限配置弹窗 -->
    <el-dialog
      v-model="permissionDialogVisible"
      :title="`配置${currentPermissionType === 'read' ? '查看' : '编辑'}权限 - ${currentUser?.username}`"
      width="800px"
      @close="closePermissionDialog"
    >
      <div class="permission-dialog-content">
        <div v-for="system in systems" :key="system.id" class="system-section">
          <div class="system-section-header">
            <h4>{{ system.name }}</h4>
            <div class="system-actions">
              <el-button
                size="small"
                @click="selectAllSystemConfigs(system.id, true)"
              >
                全选
              </el-button>
              <el-button
                size="small"
                @click="selectAllSystemConfigs(system.id, false)"
              >
                取消全选
              </el-button>
            </div>
          </div>

          <div class="configs-list">
            <el-checkbox
              v-for="config in system.configs"
              :key="config.id"
              :model-value="Boolean(tempPermissions[system.id]?.[config.name])"
              @change="updateTempPermission(system.id, config.name, $event)"
            >
              {{ config.name }}
            </el-checkbox>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closePermissionDialog">取消</el-button>
          <el-button type="primary" @click="confirmPermissionChanges">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { apiService } from '@/services/api'

// 类型定义
interface User {
  _id: string
  username: string
  role: string
  permissions: {
    read: Record<string, Record<string, any>>
    write: Record<string, Record<string, any>>
  }
  is_active: boolean
  created_at: string
  updated_at: string
  last_login: string | null
}

interface System {
  id: string
  name: string
  configs: Array<{
    id: string
    name: string
  }>
}

interface PermissionSummaryItem {
  systemId: string
  systemName: string
  configName: string
}

// 响应式数据
const loading = ref(false)
const users = ref<User[]>([])
const systems = ref<System[]>([])
const savingUsers = ref<string[]>([])

// 权限配置弹窗相关
const permissionDialogVisible = ref(false)
const currentUserId = ref<string>('')
const currentPermissionType = ref<'read' | 'write'>('read')
const currentUser = computed<User | undefined>(() => users.value.find(u => u._id === currentUserId.value))

// 临时权限数据（用于弹窗中的编辑）
const tempPermissions = reactive<Record<string, Record<string, any>>>({})

// 用户权限的本地副本（用于跟踪变更）
const userPermissionsLocal = reactive<Record<string, {
  read: Record<string, Record<string, any>>
  write: Record<string, Record<string, any>>
}>>({})

// 确保用户权限数据结构完整的辅助函数
const ensureUserPermissions = (user: User): { read: Record<string, any>, write: Record<string, any> } => {
  if (!user.permissions) {
    user.permissions = { read: {}, write: {} };
  }
  if (!user.permissions.read) {
    user.permissions.read = {};
  }
  if (!user.permissions.write) {
    user.permissions.write = {};
  }
  return user.permissions;
};

// 系统名称显示函数（直接返回原始名称）
const getSystemDisplayName = (systemId: string): string => {
  // 直接返回原始系统ID作为显示名称
  return systemId;
};

// 从用户权限数据中提取系统信息 - 优化版本
const extractSystemsFromUserPermissions = (users: User[]): System[] => {
  const systemsMap = new Map<string, System>();

  users.forEach(user => {
    // 确保用户权限数据结构完整
    const userPermissions = ensureUserPermissions(user);

    // 处理读权限和写权限
    (['read', 'write'] as const).forEach((permissionType: 'read' | 'write') => {
      // 安全地获取权限数据
      const permissions = userPermissions[permissionType] || {};

      if (permissions && typeof permissions === 'object') {
        Object.keys(permissions).forEach(systemId => {
          // 初始化系统对象
          if (!systemsMap.has(systemId)) {
            systemsMap.set(systemId, {
              id: systemId,
              name: getSystemDisplayName(systemId),
              configs: []
            });
          }

          const system = systemsMap.get(systemId)!;
          const systemPermissions = permissions[systemId];

          // 检查系统权限是否为对象类型
          if (systemPermissions && typeof systemPermissions === 'object') {
            const configNames = Object.keys(systemPermissions);

            // 添加配置项
            configNames.forEach(configName => {
              const existingConfig = system.configs.find(c => c.name === configName);
              if (!existingConfig) {
                system.configs.push({
                  id: `${systemId}_${configName}`,
                  name: configName
                });
              }
            });
          }
        });
      }
    });
  });

  const result = Array.from(systemsMap.values()).sort((a, b) => a.id.localeCompare(b.id));
  return result;
};

// 从API加载系统配置数据
const loadSystemsFromAPI = async (): Promise<void> => {
  try {
    // 调用系统配置接口
    const systemsData = await apiService.caseManagement.getSystems();

    // 解析API响应数据
    const systemsList: any[] = [];

    // 提取实际的系统数据
    let actualSystemsData = systemsData;
    if (systemsData && systemsData.data) {
      actualSystemsData = systemsData.data;
    }

    // 检查数据格式并处理
    if (actualSystemsData && typeof actualSystemsData === 'object') {
      // 遍历系统数据中的每个系统
      Object.entries(actualSystemsData).forEach(([systemKey, systemData]: [string, any]) => {

        const system = {
          id: systemKey,
          name: getSystemDisplayName(systemKey),
          configs: [] as any[]
        };

        // 解析配置项 - 从path数组中提取
        if (systemData && systemData.path && Array.isArray(systemData.path)) {
          systemData.path.forEach((pathItem: any, pathIndex: number) => {
            if (pathItem && typeof pathItem === 'object') {
              Object.keys(pathItem).forEach(configName => {
                const configData = pathItem[configName];

                // 安全检查配置数据
                if (!configData || typeof configData !== 'object') {
                  return;
                }

                system.configs.push({
                  id: `${systemKey}_${configName}`,
                  name: configName,
                  description: `${configName} 配置项`,
                  data: configData,
                  pathIndex: pathIndex
                });
              });
            }
          });
        }

        systemsList.push(system);
      });

      // 按系统ID排序
      systems.value = systemsList.sort((a, b) => a.id.localeCompare(b.id));
    } else {
      console.error('❌ 系统配置数据格式错误:', actualSystemsData);
      throw new Error('系统配置数据格式错误');
    }

  } catch (error: any) {
    console.error('❌ 加载系统配置数据失败:', error);
    ElMessage.error('加载系统配置数据失败：' + (error.message || '未知错误'));

    // 如果API调用失败，回退到从用户权限数据中提取
    systems.value = extractSystemsFromUserPermissions(users.value);
  }
};

// 加载数据
const loadData = async (): Promise<void> => {
  loading.value = true
  try {
    // 调用用户列表接口
    const usersData = await apiService.permission.getMultiUserAdminUsers();

    // 处理用户数据 - 更加健壮的数据提取
    let userData = [];

    try {
      if (!usersData) {
        userData = [];
      } else if (Array.isArray(usersData)) {
        // 直接返回用户数组
        userData = usersData;
      } else if (usersData.users && Array.isArray(usersData.users)) {
        // 包装格式: {users: [...]}
        userData = usersData.users;
      } else if (usersData.data && Array.isArray(usersData.data)) {
        // 包装格式: {data: [...]}
        userData = usersData.data;
      } else if (usersData.data && usersData.data.users && Array.isArray(usersData.data.users)) {
        // 嵌套包装格式: {data: {users: [...]}}
        userData = usersData.data.users;
      } else {
        // 尝试查找任何数组字段
        for (const key in usersData) {
          if (Array.isArray(usersData[key])) {
            userData = usersData[key];
            break;
          }
        }

        if (userData.length === 0) {
          throw new Error('用户数据格式错误：无法找到用户数组');
        }
      }

      users.value = userData;

    } catch (error: any) {
      console.error('❌ 处理用户数据时出错:', error);
      users.value = [];
      throw new Error('用户数据处理失败: ' + (error.message || '未知错误'));
    }

    // 从系统配置接口获取系统信息
    await loadSystemsFromAPI();

    // 初始化本地权限副本 - 安全版本
    users.value.forEach(user => {
      // 确保用户权限数据结构完整
      const userPermissions = ensureUserPermissions(user);

      // 深拷贝权限数据，确保嵌套对象正确复制
      userPermissionsLocal[user._id] = {
        read: JSON.parse(JSON.stringify(userPermissions.read)),
        write: JSON.parse(JSON.stringify(userPermissions.write))
      };
    });

    ElMessage.success(`数据加载成功 - 用户: ${users.value.length}, 系统: ${systems.value.length}`);
  } catch (error: any) {
    ElMessage.error(`加载数据失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 获取系统的查看权限用户 - 安全版本
const getSystemReadUsers = (systemId: string): User[] => {
  const result = users.value.filter(user => {
    // 安全地检查权限数据
    if (!user.permissions?.read) return false

    const systemPermissions = user.permissions?.read?.[systemId]
    const hasPermission = systemPermissions &&
                         typeof systemPermissions === 'object' &&
                         Object.keys(systemPermissions).length > 0

    return hasPermission
  })

  return result
}

// 获取系统的编辑权限用户 - 安全版本
const getSystemWriteUsers = (systemId: string): User[] => {
  const result = users.value.filter(user => {
    // 安全地检查权限数据
    if (!user.permissions?.write) return false

    const systemPermissions = user.permissions?.write?.[systemId]
    const hasPermission = systemPermissions &&
                         typeof systemPermissions === 'object' &&
                         Object.keys(systemPermissions).length > 0

    return hasPermission
  })

  return result
}

// 获取用户权限摘要 - 安全版本
const getUserPermissionSummary = (userId: string, type: 'read' | 'write'): PermissionSummaryItem[] => {
  const userPerms = userPermissionsLocal[userId]
  if (!userPerms) {
    return []
  }

  const summary: PermissionSummaryItem[] = []
  // 安全地获取权限数据
  const permissions = userPerms[type] || {}

  if (permissions && typeof permissions === 'object') {
    Object.keys(permissions).forEach(systemId => {
      const system = systems.value.find(s => s.id === systemId)
      const systemPermissions = permissions[systemId]

      if (system && systemPermissions && typeof systemPermissions === 'object') {
        Object.keys(systemPermissions).forEach(configName => {
          summary.push({
            systemId,
            systemName: system.name,
            configName
          })
        })
      }
    })
  }

  return summary
}

// 检查用户是否有权限变更 - 安全版本
const hasPermissionChanges = (userId: string): boolean => {
  const user = users.value.find(u => u._id === userId)
  const localPerms = userPermissionsLocal[userId]

  if (!user || !localPerms) return false

  // 安全地获取用户权限数据
  const userRead = user.permissions?.read || {}
  const userWrite = user.permissions?.write || {}
  const localRead = localPerms.read || {}
  const localWrite = localPerms.write || {}

  // 比较读权限和写权限
  const readChanged = JSON.stringify(userRead) !== JSON.stringify(localRead)
  const writeChanged = JSON.stringify(userWrite) !== JSON.stringify(localWrite)

  return readChanged || writeChanged
}

// 滚动到指定用户卡片
const scrollToUser = (userId: string): void => {
  const element = document.getElementById(`user-card-${userId}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    // 添加高亮效果
    element.classList.add('highlight')
    setTimeout(() => {
      element.classList.remove('highlight')
    }, 2000)
  }
}

// 打开权限配置弹窗 - 优化版本
const openPermissionDialog = (userId: string, type: 'read' | 'write'): void => {
  currentUserId.value = userId
  currentPermissionType.value = type

  // 清空临时权限数据
  Object.keys(tempPermissions).forEach(key => delete tempPermissions[key])

  const userPerms = userPermissionsLocal[userId]

  if (userPerms) {
    // 安全地获取权限数据
    const permissions = userPerms[type] || {}

    if (permissions && typeof permissions === 'object') {
      Object.keys(permissions).forEach(systemId => {
        const systemPermissions = permissions[systemId]
        if (systemPermissions && typeof systemPermissions === 'object') {
          // 深拷贝权限数据
          tempPermissions[systemId] = { ...systemPermissions }
        }
      })
    }
  }

  permissionDialogVisible.value = true
}

// 关闭权限配置弹窗
const closePermissionDialog = (): void => {
  permissionDialogVisible.value = false
  currentUserId.value = ''
  Object.keys(tempPermissions).forEach(key => delete tempPermissions[key])
}

// 更新临时权限
const updateTempPermission = (systemId: string, configName: string, value: boolean | string | number): void => {
  if (!tempPermissions[systemId]) {
    tempPermissions[systemId] = {}
  }

  if (value) {
    tempPermissions[systemId][configName] = {}
  } else {
    delete tempPermissions[systemId][configName]
    // 如果系统下没有配置项了，删除整个系统
    if (Object.keys(tempPermissions[systemId]).length === 0) {
      delete tempPermissions[systemId]
    }
  }
}

// 选择/取消选择系统下的所有配置项
const selectAllSystemConfigs = (systemId: string, select: boolean): void => {
  const system = systems.value.find(s => s.id === systemId)
  if (!system) return

  if (select) {
    if (!tempPermissions[systemId]) {
      tempPermissions[systemId] = {}
    }
    system.configs.forEach(config => {
      tempPermissions[systemId][config.name] = {}
    })
  } else {
    delete tempPermissions[systemId]
  }
}

// 确认权限变更 - 优化版本
const confirmPermissionChanges = (): void => {
  if (!currentUserId.value) return

  // 清空当前权限类型的数据
  userPermissionsLocal[currentUserId.value][currentPermissionType.value] = {}

  // 更新本地权限数据
  Object.keys(tempPermissions).forEach(systemId => {
    if (Object.keys(tempPermissions[systemId]).length > 0) {
      userPermissionsLocal[currentUserId.value][currentPermissionType.value][systemId] = { ...tempPermissions[systemId] }
    }
  })

  closePermissionDialog()
  ElMessage.success('权限配置已更新，请点击"确定"按钮保存')
}

// 保存用户权限
const saveUserPermissions = async (userId: string): Promise<void> => {
  if (savingUsers.value.includes(userId)) return

  try {
    savingUsers.value.push(userId)

    const permissions = userPermissionsLocal[userId]
    await apiService.permission.setUserPermissions(userId, permissions)

    // 更新原始用户数据 - 安全版本
    const user = users.value.find(u => u._id === userId)
    if (user) {
      // 确保用户有权限对象
      if (!user.permissions) {
        user.permissions = { read: {}, write: {} }
      }

      // 安全地更新权限数据
      user.permissions = {
        read: { ...permissions.read },
        write: { ...permissions.write }
      }
    }

    ElMessage.success('用户权限保存成功')
  } catch (error: any) {
    ElMessage.error(`保存权限失败: ${error.message || '未知错误'}`)
  } finally {
    savingUsers.value = savingUsers.value.filter(id => id !== userId)
  }
}

// 组件挂载时加载数据
onMounted(async (): Promise<void> => {
  try {
    await loadData()
  } catch (error: any) {
    console.error('组件挂载时加载数据失败:', error)
  }
})
</script>

<style scoped lang="scss">
.user-permission-assignment {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;

  .page-title {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 28px;
  }

  .page-subtitle {
    margin: 0;
    color: #606266;
    font-size: 16px;
  }
}

.permission-overview {
  margin-bottom: 40px;

  h2 {
    margin: 0 0 20px 0;
    color: #303133;
    font-size: 20px;
  }
}

.systems-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.system-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;

  .system-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #303133;
      font-size: 16px;
    }

    .config-count {
      color: #909399;
      font-size: 14px;
    }
  }

  .permission-stats {
    .stat-item {
      margin-bottom: 12px;

      .stat-label {
        display: block;
        margin-bottom: 8px;
        color: #606266;
        font-size: 14px;
        font-weight: 500;
      }

      .user-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .clickable-tag {
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }

        .no-users {
          color: #c0c4cc;
          font-style: italic;
        }
      }
    }
  }
}

.users-permission-section {
  h2 {
    margin: 0 0 20px 0;
    color: #303133;
    font-size: 20px;
  }
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.user-card {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s;

  &.highlight {
    border-color: #409eff;
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }

  .user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;

      h3 {
        margin: 0;
        color: #303133;
        font-size: 16px;
      }
    }
  }

  .permission-types {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .permission-type-card {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;

    .permission-type-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 14px;
      }
    }

    .permission-summary {
      .no-permissions {
        color: #c0c4cc;
        font-style: italic;
        font-size: 14px;
      }

      .permission-items {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .permission-tag {
          font-size: 12px;
        }
      }
    }
  }
}

.permission-dialog-content {
  max-height: 500px;
  overflow-y: auto;

  .system-section {
    margin-bottom: 24px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 16px;

    .system-section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
      }

      .system-actions {
        display: flex;
        gap: 8px;
      }
    }

    .configs-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.no-systems {
  padding: 40px 20px;
  text-align: center;
}
</style>
