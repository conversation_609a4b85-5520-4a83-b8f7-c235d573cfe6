import { ref, reactive, computed } from 'vue'
import { IsolatedApiService } from '@/services/isolatedApiService'
import { IndexedDBService } from '@/services/indexedDBService'
import { ShadowDOMManager } from '@/utils/shadowDOMManager'
import { tokenManager } from '@/utils/tokenManager'
import type { UserProfile } from '@/types/api/user'

// 用户状态接口
interface UserState {
  id: string
  username: string
  profile: UserProfile | null
  token: string | null
  isActive: boolean
  isOnline: boolean
  lastActivity: number
  proxyChannel: string
  shadowRoot: ShadowRoot | null
  dbService: any // 使用 any 类型避免类型检查问题
}

// 最大同时在线用户数
const MAX_CONCURRENT_USERS = 50

// 全局状态
const users = reactive<Map<string, UserState>>(new Map())
const currentUserId = ref<string | null>(null)
const shadowDOMManager = new ShadowDOMManager()

export function useMultiUserSystem() {
  // 计算属性
  const currentUser = computed(() => {
    if (!currentUserId.value) return null
    return users.get(currentUserId.value) || null
  })

  const onlineUsers = computed(() => {
    return Array.from(users.values()).filter((user) => user.isOnline)
  })

  const canAddUser = computed(() => {
    return onlineUsers.value.length < MAX_CONCURRENT_USERS
  })

  /**
   * 生成唯一的用户ID
   * 对用户名进行Base64编码以避免HTTP请求头编码问题
   */
  function generateUserId(username: string): string {
    // 对用户名进行Base64编码，确保只包含ASCII字符
    const encodedUsername = btoa(encodeURIComponent(username))
    return `user_${encodedUsername}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 创建用户状态
   */
  async function createUserState(username: string): Promise<UserState> {
    const userId = generateUserId(username)
    const proxyChannel = `proxy_${userId}`

    // 创建独立的IndexedDB服务
    const dbService = new IndexedDBService(userId)
    await dbService.init()

    // 创建影子DOM容器
    const shadowRoot = shadowDOMManager.createShadowRoot(userId)

    const userState: UserState = {
      id: userId,
      username,
      profile: null,
      token: null,
      isActive: false,
      isOnline: false,
      lastActivity: Date.now(),
      proxyChannel,
      shadowRoot,
      dbService,
    }

    return userState
  }

  /**
   * 用户登录
   */
  async function login(
    username: string,
    password: string,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      // 检查是否已达到最大用户数
      if (!canAddUser.value) {
        return { success: false, message: '已达到最大同时在线用户数限制' }
      }

      // 创建用户状态
      const userState = await createUserState(username)

      // 尝试登录
      const apiService = new IsolatedApiService(userState.id, userState.proxyChannel)
      const result = await apiService.login({ username, password })

      if (result.success && result.data) {
        // 更新用户状态
        userState.profile = result.data.user_info
        userState.token = result.data.auth_token
        userState.isActive = true
        userState.isOnline = true
        userState.lastActivity = Date.now()

        // 存储用户状态
        users.set(userState.id, userState)

        // 设置为当前用户
        currentUserId.value = userState.id

        // 保存到IndexedDB
        await userState.dbService.setItem('profile', userState.profile)
        await userState.dbService.setItem('token', userState.token)
        await userState.dbService.setItem('loginTime', Date.now())

        // 同步token到主要的认证系统，确保其他API请求能正常工作
        const { useAuthStore } = await import('@/stores/auth')
        const authStore = useAuthStore()

        // 直接设置用户信息和token
        authStore.userProfile = userState.profile
        authStore.accessToken = userState.token
        authStore.refreshToken = userState.token

        // 保存到存储
        const tokenInfo = {
          access_token: userState.token,
          refresh_token: userState.token,
          expires_at: result.data.expires_at || '',
          expires_in: result.data.expires_in || 24 * 60 * 60,
          token_type: 'Bearer'
        }
        authStore.saveTokenToStorage(tokenInfo, userState.profile)

        return { success: true }
      } else {
        // 清理失败的用户状态
        shadowDOMManager.removeShadowRoot(userState.id)
        await userState.dbService.clear()

        return { success: false, message: result.message || '登录失败' }
      }
    } catch (error: any) {
      return { success: false, message: error.message || '登录时发生错误' }
    }
  }

  /**
   * 清理用户状态的通用方法
   */
  async function cleanupUserState(userState: UserState, removeFromUsers: boolean = true): Promise<void> {
    // 调用API登出
    const apiService = new IsolatedApiService(userState.id, userState.proxyChannel)

    // 使用新的登出接口格式
    const token = userState.token || tokenManager.getAccessToken()
    if (token) {
      await apiService.post('/v1/multi-user/auth/logout', {
        auth_token: token
      })
    } else {
      // 如果没有token，仍然调用标准登出方法
      await apiService.logout()
    }

    // 清理用户状态
    userState.isActive = false
    userState.isOnline = false
    userState.token = null
    userState.profile = null

    // 清理影子DOM
    shadowDOMManager.removeShadowRoot(userState.id)

    // 清理IndexedDB
    await userState.dbService.clear()

    // 从用户映射中删除（可选）
    if (removeFromUsers) {
      users.delete(userState.id)
    }
  }

  /**
   * 用户登出
   */
  async function logout(userId?: string): Promise<{ success: boolean; message?: string }> {
    const targetUserId = userId || currentUserId.value
    if (!targetUserId) {
      return { success: false, message: '没有指定用户' }
    }

    const userState = users.get(targetUserId)
    if (!userState) {
      return { success: false, message: '用户不存在' }
    }

    try {
      await cleanupUserState(userState, true)

      // 如果是当前用户，切换到其他用户或清空
      if (currentUserId.value === targetUserId) {
        const otherUsers = Array.from(users.keys())
        currentUserId.value = otherUsers.length > 0 ? otherUsers[0] : null
      }

      return { success: true }
    } catch (error: any) {
      return { success: false, message: error.message || '登出时发生错误' }
    }
  }

  /**
   * 切换用户
   */
  function switchUser(userId: string): boolean {
    if (!users.has(userId)) {
      return false
    }

    const userState = users.get(userId)!
    if (!userState.isOnline) {
      return false
    }

    // 停用当前用户
    if (currentUserId.value) {
      const currentUserState = users.get(currentUserId.value)
      if (currentUserState) {
        currentUserState.isActive = false
      }
    }

    // 激活新用户
    userState.isActive = true
    userState.lastActivity = Date.now()
    currentUserId.value = userId

    return true
  }

  /**
   * 获取用户的API服务
   */
  function getUserApiService(userId?: string): IsolatedApiService | null {
    const targetUserId = userId || currentUserId.value
    if (!targetUserId) return null

    const userState = users.get(targetUserId)
    if (!userState) return null

    return new IsolatedApiService(userState.id, userState.proxyChannel)
  }

  /**
   * 获取用户的数据库服务
   */
  function getUserDBService(userId?: string): any {
    const targetUserId = userId || currentUserId.value
    if (!targetUserId) return null

    const userState = users.get(targetUserId)
    if (!userState) return null

    return userState.dbService
  }

  // 删除未使用的getUserShadowRoot方法

  // 删除未使用的clearAllUsers方法

  /**
   * 根据用户名查找用户状态
   */
  function findUserByUsername(username: string): UserState | null {
    for (const userState of users.values()) {
      if (userState.username === username) {
        return userState
      }
    }
    return null
  }

  /**
   * 强制指定用户退出（用于管理员操作）
   */
  async function forceLogoutUser(username: string, reason?: string): Promise<{ success: boolean; message?: string }> {
    const userState = findUserByUsername(username)
    if (!userState) {
      return { success: false, message: `用户 ${username} 不存在或未登录` }
    }

    try {
      console.log(`强制退出用户: ${username}, 原因: ${reason || '管理员操作'}`)

      await cleanupUserState(userState, true)

      // 如果是当前用户，清空当前用户ID
      if (currentUserId.value === userState.id) {
        currentUserId.value = null
      }

      return { success: true, message: `用户 ${username} 已强制退出` }
    } catch (error) {
      console.error(`强制退出用户 ${username} 失败:`, error)
      return { success: false, message: `强制退出失败: ${error}` }
    }
  }

  /**
   * 获取系统状态
   */
  function getSystemStatus() {
    return {
      totalUsers: users.size,
      onlineUsers: onlineUsers.value.length,
      maxUsers: MAX_CONCURRENT_USERS,
      canAddUser: canAddUser.value,
      currentUser: currentUser.value?.username || null,
      users: Array.from(users.values()).map((user) => ({
        id: user.id,
        username: user.username,
        isActive: user.isActive,
        isOnline: user.isOnline,
        lastActivity: user.lastActivity,
      })),
    }
  }

  return {
    // 状态
    currentUser,
    onlineUsers,
    canAddUser,

    // 方法
    login,
    logout,
    switchUser,
    getUserApiService,
    getUserDBService,
    getSystemStatus,

    // 工具方法
    findUserByUsername,
    forceLogoutUser,
  }
}
